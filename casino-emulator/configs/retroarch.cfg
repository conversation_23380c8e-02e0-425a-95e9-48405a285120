# RetroArch Configuration for Casino Emulator
# Basic configuration for running games with AI override

# Video settings
video_driver = "gl"
video_width = 1920
video_height = 1080
video_fullscreen = false
video_windowed_fullscreen = false
video_vsync = true
video_smooth = true
video_force_aspect = true
video_aspect_ratio_auto = true

# Audio settings
audio_driver = "pulse"
audio_enable = true
audio_out_rate = 48000
audio_latency = 64

# Input settings
input_driver = "udev"
input_autodetect_enable = true
input_joypad_driver = "udev"

# Core settings
libretro_directory = "/usr/lib/libretro"
libretro_info_path = "/usr/share/libretro/info"
content_database_path = "/usr/share/libretro/database/rdb"
cheat_database_path = "/usr/share/libretro/database/cht"
cursor_directory = "/usr/share/pixmaps"

# System directories
system_directory = "/work/system"
savestate_directory = "/work/savestates"
savefile_directory = "/work/saves"
screenshot_directory = "/work/screenshots"

# Menu settings
menu_driver = "rgui"
menu_show_core_updater = false
menu_show_online_updater = false

# AI Controller Plugin Settings
ai_controller_enable = true
ai_controller_config_path = "/work/configs/ai-controller.cfg"

# Core-specific settings for Genesis Plus GX (Mortal Kombat 2)
genesis_plus_gx_bram = "per_game"
genesis_plus_gx_region = "auto"
genesis_plus_gx_system_hw = "auto"

# Performance settings
rewind_enable = false
run_ahead_enabled = false
fps_show = true

# Network settings (for web access)
network_cmd_enable = true
network_cmd_port = 55355

# Logging
log_verbosity = true
frontend_log_level = 1
