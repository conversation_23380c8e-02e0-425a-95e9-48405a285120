# AI Controller Configuration for Mortal Kombat 2 Genesis
# This config file specifies which PyTorch models to load for AI players

retro_ai_lib {
  # Player 1 AI Model (<PERSON>)
  player1_model_path = "/work/models/Akira.pt"
  
  # Player 2 AI Model (<PERSON>)
  player2_model_path = "/work/models/<PERSON>Kang.pt"
  
  # Device to run inference on (cpu for now, gpu later)
  device = "cpu"
  
  # Game-specific input mapping for Mortal Kombat 2 Genesis
  game = "MortalKombatII-Genesis"
  
  # Input button mapping for Genesis controller
  # Standard Genesis controller: A, B, C, X, Y, Z, Start, Mode
  input_map = {
    "A": 0,      # Punch
    "B": 1,      # Kick  
    "C": 2,      # Block
    "X": 3,      # Run
    "Y": 4,      # High Punch
    "Z": 5,      # High Kick
    "START": 6,  # Start/Pause
    "MODE": 7,   # Mode
    "UP": 8,     # D-pad Up
    "DOWN": 9,   # D-pad Down
    "LEFT": 10,  # D-pad Left
    "RIGHT": 11  # D-pad Right
  }
  
  # Enable AI override for both players
  override_player1 = true
  override_player2 = true
  
  # Model inference settings
  inference_frequency = 60  # Hz, match game framerate
  action_repeat = 1         # How many frames to repeat each action
}
