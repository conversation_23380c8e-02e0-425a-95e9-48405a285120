# syntax=docker/dockerfile:1
FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PATH=/opt/conda/bin:$PATH

# 1. System dependencies
RUN apt-get update && apt-get install -y \
    git cmake pkg-config zlib1g-dev libopenmpi-dev ffmpeg \
    libx11-dev libegl1-mesa-dev libgbm-dev libvulkan-dev \
    libpulse-dev libopenal-dev wget bzip2 libopencv-dev \
 && rm -rf /var/lib/apt/lists/*

# 2. Install Miniconda
RUN wget -q https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh \
    -O /tmp/conda.sh \
 && bash /tmp/conda.sh -b -p /opt/conda \
 && rm /tmp/conda.sh \
 && conda clean -afy

# 3. Create Python environment with essential ML libs
RUN conda create -n casino python=3.10 -y \
 && conda run -n casino pip install \
      "stable-baselines3[extra]" gymnasium torch torchvision opencv-python timm

# 4. Clone and install stable‑retro & scripts, including retro_ai_lib
RUN git clone https://github.com/Farama-Foundation/stable-retro.git /src/stable-retro \
 && git clone https://github.com/MatPoliquin/stable-retro-scripts.git /src/stable-retro-scripts \
 && cd /src/stable-retro \
 && conda run -n casino pip install -e . \
 && cd /src/stable-retro-scripts \
 && conda run -n casino pip install -e . \
 # Build retro_ai_lib C++ plugin
 && mkdir -p retro_ai_lib/build \
 && cd retro_ai_lib/build \
 && cmake -DUSE_PYTORCH=ON .. \
 && make -j$(nproc)

# 5. Clone & build RetroArchAI with retro_ai_lib linked
RUN git clone https://github.com/MatPoliquin/RetroArchAI.git /src/RetroArchAI \
 && mkdir -p /src/RetroArchAI/build \
 && cd /src/RetroArchAI/build \
 # Provide path to retro_ai_lib for CMake integration
 && cmake -DRETRO_AI_LIB_DIR=/src/stable-retro-scripts/retro_ai_lib/build .. \
 && make -j$(nproc)

# 6. Set working directory & default to interactive shell
WORKDIR /work
CMD ["bash"]
