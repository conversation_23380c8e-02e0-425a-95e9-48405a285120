# 🎰 Casino‑Emulator

This repository sets up **casino‑emulator**: a Docker-based environment that brings together:

- 🧩 **RetroArchAI** – a RetroArch fork for AI-controlled gaming
- 🧠 **stable‑retro** + **stable‑retro-scripts** – RL training & AI input plumbing
- 🎮 **retro_ai_lib** – C++ plugin for AI input override using PyTorch
- ⚙️ A Next.js frontend enabling users to pit ML models against each other

ROMs are preloaded on your end; users select AI agents to compete in classic games via a web UI.

---

## 📂 Project Structure

casino-emulator/
├── docker/
│ └── Dockerfile # Builds full runtime container
├── src/
│ ├── stable-retro/ # Editable clone
│ ├── stable-retro-scripts/
│ └── RetroArchAI/
├── roms/ # Place ROMs here
├── models/
│ └── agentXYZ/ # Training artifacts
├── front/ # Next.js UI
├── configs/
│ ├── ai-controller.cfg # retro_ai_lib input config
│ └── retroarch.cfg # emulator config
├── scripts/
│ ├── train.sh
│ └── serve.sh
└── README.md # You are here

yaml
Copy
Edit

---

## 🚀 Quickstart

### 🛠 Build Docker

```bash
docker build -t casino-emulator -f docker/Dockerfile .
🧩 Run Container
bash
Copy
Edit
docker run -it --rm \
  -v $PWD/roms:/work/roms \
  -v $PWD/configs:/work/configs \
  -v $PWD/models:/work/models \
  -p 3000:3000 \      # for frontend
  casino-emulator
Inside container:

bash
Copy
Edit
conda activate casino
python3 -m retro.import roms/
⚙️ Training Agents
Example scripts/train.sh:

bash
Copy
Edit
#!/usr/bin/env bash
conda activate casino
python -m stable_retro_scripts.train \
  --game "SonicTheHedgehog-Genesis" \
  --algo "PPO" \
  --output-dir "/work/models/sonic_ppo"
🕹 Using RetroArchAI
Use configs/ai-controller.cfg to specify which PyTorch model to load:

cfg
Copy
Edit
retro_ai_lib {
  model_path = "/work/models/sonic_ppo/best_model.zip"
  device = "cpu"
  input_map = ...
}
Launch emulator:

bash
Copy
Edit
/src/RetroArchAI/build/retroarch \
  --config "/work/configs/retroarch.cfg" \
  --controller-plugin "/work/configs/ai-controller.cfg"