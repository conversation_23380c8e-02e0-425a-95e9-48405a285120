FROM --platform=linux/amd64 ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/conda/bin:/usr/local/bin:${PATH}" \
    PYTHONPATH="/work:/work/stable-retro:/work/stable-retro-scripts:${PYTHONPATH:-}" \
    DISPLAY=:99 \
    SDL_AUDIODRIVER=alsa \
    AUDIODEV=hw:0,0

# Install system dependencies (based on working docker_emulator)
RUN apt-get update && apt-get install -y \
    git python3-pip python3-dev zlib1g-dev libopenmpi-dev cmake wget unzip nano \
    libqt5opengl5-dev qtbase5-dev libopencv-dev libhdf5-dev libhdf5-serial-dev hdf5-tools \
    build-essential nodejs npm netcat bzip2 ca-certificates libx11-dev libxcb1-dev \
    libx11-xcb-dev libxcb-keysyms1-dev libxcb-image0-dev libxcb-shm0-dev libxcb-icccm4-dev \
    libxcb-sync0-dev libxcb-xfixes0-dev libxcb-shape0-dev libxcb-randr0-dev \
    libxcb-render-util0-dev libxcb-xinerama0-dev libx264-dev libmp3lame-dev libgl1-mesa-glx \
    libglib2.0-0 strace net-tools curl nasm yasm xvfb x11vnc \
    htop iotop lsof tcpdump vim less procps \
    libass-dev libfreetype6-dev libsdl2-dev libtool libva-dev libvdpau-dev \
    libvorbis-dev libxcb1-dev libxcb-shm0-dev libxcb-xfixes0-dev pkg-config \
    texinfo zlib1g-dev libavdevice-dev libavfilter-dev libopus-dev \
    libvpx-dev libwebp-dev libx265-dev libnuma-dev libfdk-aac-dev \
    libxcomposite-dev libxinerama-dev libv4l-dev libxrandr-dev libxext-dev \
    libxrender-dev libxau-dev libxfixes-dev libasound2-dev openssl \
    alsa-utils \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda (following working pattern)
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh && \
    /bin/bash ~/miniconda.sh -b -p /opt/conda && \
    rm ~/miniconda.sh && \
    /opt/conda/bin/conda config --set always_yes yes --set changeps1 no && \
    /opt/conda/bin/conda create -n casino python=3.10 -y

SHELL ["conda", "run", "-n", "casino", "/bin/bash", "-c"]

# Install PyTorch and ML packages using conda (following working pattern)
RUN conda install -y -c pytorch pytorch torchvision torchaudio cpuonly && \
    pip install gymnasium stable-baselines3 opencv-python numpy

# Install and build stable-retro (following working pattern)
RUN git clone https://github.com/Farama-Foundation/stable-retro.git /work/stable-retro && \
    cd /work/stable-retro && \
    pip install -e . && \
    python3 -m retro.import /work/stable-retro/retro/data/stable

# Install and build stable-retro-scripts with retro_ai_lib (following working pattern)
RUN git clone https://github.com/MatPoliquin/stable-retro-scripts.git /work/stable-retro-scripts && \
    cd /work/stable-retro-scripts/retro_ai_lib && \
    wget https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-2.3.1%2Bcpu.zip && \
    unzip libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip && \
    rm libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip && \
    cmake . -DCMAKE_PREFIX_PATH=$(pwd)/libtorch && \
    make

# Install stable-retro-scripts Python package
RUN cd /work/stable-retro-scripts && pip install -e .

# Clone and build RetroArchAI
RUN git clone https://github.com/MatPoliquin/RetroArchAI.git /work/RetroArchAI && \
    cd /work/RetroArchAI && \
    ./configure --enable-retro-ai-lib --with-retro-ai-lib-dir=/work/stable-retro-scripts/retro_ai_lib && \
    make -j$(nproc)

# Create working directories
RUN mkdir -p /work/roms /work/configs /work/models /work/system /work/savestates /work/saves /work/screenshots

# Copy configuration files
COPY configs/ /work/configs/

# Copy models if they exist
COPY models/ /work/models/

# Set library paths (following working pattern)
ENV LD_LIBRARY_PATH="/work/stable-retro-scripts/retro_ai_lib:/work/stable-retro-scripts/retro_ai_lib/libtorch/lib:$LD_LIBRARY_PATH"

# Set working directory
WORKDIR /work

# Expose ports for RetroArch network commands and web access
EXPOSE 55355 8080

# Default command starts bash for interactive use
CMD ["bash"]
