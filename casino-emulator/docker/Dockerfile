# syntax=docker/dockerfile:1
FROM --platform=linux/amd64 ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Install all system dependencies for building from source
RUN apt-get update && apt-get install -y \
    # Core build tools
    git cmake pkg-config build-essential gcc g++ make \
    # Python and development tools
    python3 python3-pip python3-dev python3-venv \
    # System libraries for stable-retro
    zlib1g-dev libopenmpi-dev ffmpeg \
    # Graphics and audio libraries for RetroArch
    libx11-dev libegl1-mesa-dev libgbm-dev libvulkan-dev \
    libpulse-dev libopenal-dev libdrm-dev \
    # Additional dependencies for retro_ai_lib and RetroArchAI
    libopencv-dev libssl-dev \
    # Utilities
    wget unzip bzip2 curl \
 && rm -rf /var/lib/apt/lists/*

# Create Python virtual environment
RUN python3 -m venv /opt/casino-env \
 && /opt/casino-env/bin/pip install --upgrade pip setuptools wheel

# Set environment to use virtual environment
ENV PATH="/opt/casino-env/bin:$PATH"
ENV VIRTUAL_ENV="/opt/casino-env"

# Install Python ML packages
RUN pip install \
    gymnasium \
    torch torchvision --index-url https://download.pytorch.org/whl/cpu \
    opencv-python \
    stable-baselines3 \
    numpy

# Clone all required repositories
RUN git clone https://github.com/Farama-Foundation/stable-retro.git /src/stable-retro \
 && git clone https://github.com/MatPoliquin/stable-retro-scripts.git /src/stable-retro-scripts \
 && git clone https://github.com/MatPoliquin/RetroArchAI.git /src/RetroArchAI

# Install stable-retro
RUN cd /src/stable-retro && pip install -e .

# Install stable-retro-scripts
RUN cd /src/stable-retro-scripts && pip install -e .

# Download PyTorch C++ library for retro_ai_lib
RUN cd /src/stable-retro-scripts/retro_ai_lib \
 && wget -q https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-2.3.1%2Bcpu.zip \
 && unzip -q libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip \
 && rm libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip

# Build retro_ai_lib (the C++ library that interfaces with PyTorch)
RUN cd /src/stable-retro-scripts/retro_ai_lib \
 && mkdir -p build \
 && cd build \
 && cmake -DCMAKE_PREFIX_PATH=../libtorch -DUSE_PYTORCH=ON .. \
 && make -j$(nproc)

# Build RetroArchAI with retro_ai_lib integration
RUN cd /src/RetroArchAI \
 && ./configure --enable-retro-ai-lib --with-retro-ai-lib-dir=/src/stable-retro-scripts/retro_ai_lib/build \
 && make -j$(nproc)

# Create working directories and copy configs
RUN mkdir -p /work/roms /work/configs /work/models /work/system /work/savestates /work/saves /work/screenshots

# Copy configuration files
COPY configs/ /work/configs/

# Set library paths
ENV LD_LIBRARY_PATH="/src/stable-retro-scripts/retro_ai_lib/build:/src/stable-retro-scripts/retro_ai_lib/libtorch/lib:$LD_LIBRARY_PATH"
ENV PYTHONPATH="/src/stable-retro:/src/stable-retro-scripts:$PYTHONPATH"

# Expose ports for web access
EXPOSE 8080 55355

# Set working directory
WORKDIR /work

# Default command starts bash for interactive use
CMD ["bash"]
