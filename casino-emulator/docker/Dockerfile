# syntax=docker/dockerfile:1
FROM --platform=linux/amd64 ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Install all system dependencies in one layer
RUN apt-get update && apt-get install -y \
    # Core build tools
    git cmake pkg-config build-essential \
    # Python and pip
    python3 python3-pip python3-venv \
    # System libraries for stable-retro
    zlib1g-dev libopenmpi-dev ffmpeg \
    # Graphics and audio libraries
    libx11-dev libegl1-mesa-dev libgbm-dev libvulkan-dev \
    libpulse-dev libopenal-dev \
    # Additional dependencies for retro_ai_lib
    libqt5opengl5-dev qtbase5-dev libopencv-dev \
    # Utilities
    wget unzip bzip2 \
 && rm -rf /var/lib/apt/lists/*

# Create Python virtual environment and install core ML packages
RUN python3 -m venv /opt/casino-env \
 && /opt/casino-env/bin/pip install --upgrade pip setuptools wheel

# Set environment to use virtual environment
ENV PATH="/opt/casino-env/bin:$PATH"
ENV VIRTUAL_ENV="/opt/casino-env"

# Install Python ML packages
RUN pip install \
    gymnasium \
    torch torchvision --index-url https://download.pytorch.org/whl/cpu \
    opencv-python \
    stable-baselines3

# Clone repositories
RUN git clone https://github.com/Farama-Foundation/stable-retro.git /src/stable-retro \
 && git clone https://github.com/MatPoliquin/stable-retro-scripts.git /src/stable-retro-scripts \
 && git clone https://github.com/MatPoliquin/RetroArchAI.git /src/RetroArchAI

# Install stable-retro
RUN cd /src/stable-retro && pip install -e .

# Install stable-retro-scripts
RUN cd /src/stable-retro-scripts && pip install -e .

# Download and setup PyTorch C++ library for retro_ai_lib
RUN cd /src/stable-retro-scripts/retro_ai_lib \
 && wget -q https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-2.3.1%2Bcpu.zip \
 && unzip -q libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip \
 && rm libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip

# Build retro_ai_lib
RUN cd /src/stable-retro-scripts/retro_ai_lib \
 && mkdir -p build \
 && cd build \
 && cmake -DCMAKE_PREFIX_PATH=../libtorch .. \
 && make -j$(nproc)

# Build RetroArchAI
RUN cd /src/RetroArchAI \
 && mkdir -p build \
 && cd build \
 && cmake -DRETRO_AI_LIB_DIR=/src/stable-retro-scripts/retro_ai_lib/build .. \
 && make -j$(nproc)

# Create working directories
RUN mkdir -p /work/roms /work/configs /work/models /work/system /work/savestates /work/saves /work/screenshots

# Set library path for retro_ai_lib
ENV LD_LIBRARY_PATH="/src/stable-retro-scripts/retro_ai_lib/build:$LD_LIBRARY_PATH"

# Set working directory
WORKDIR /work

# Default command
CMD ["bash"]
