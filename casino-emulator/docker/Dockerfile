FROM --platform=linux/amd64 ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Install only essential dependencies
RUN apt-get update && apt-get install -y \
    git cmake build-essential wget unzip python3 python3-pip python3-dev \
    zlib1g-dev libopenmpi-dev pkg-config libopencv-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /work

# Install Python packages directly
RUN pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu && \
    pip install gymnasium stable-baselines3 opencv-python numpy

# Clone and install stable-retro (following working docker_emulator pattern)
RUN git clone https://github.com/Farama-Foundation/stable-retro.git && \
    cd stable-retro && \
    pip install -e . && \
    python3 -m retro.import /work/stable-retro/retro/data/stable

# Clone stable-retro-scripts and build ef_lib (following working docker_emulator pattern)
RUN git clone https://github.com/MatPoliquin/stable-retro-scripts.git && \
    cd stable-retro-scripts/ef_lib && \
    wget https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-1.13.1%2Bcpu.zip && \
    unzip libtorch-cxx11-abi-shared-with-deps-1.13.1+cpu.zip && \
    rm libtorch-cxx11-abi-shared-with-deps-1.13.1+cpu.zip && \
    cmake . -DCMAKE_PREFIX_PATH=$(pwd)/libtorch && \
    make

# Clone and build RetroArchAI (following README instructions)
RUN git clone https://github.com/MatPoliquin/RetroArchAI.git && \
    cd RetroArchAI && \
    ./configure && \
    make -j$(nproc)

# Create necessary directories
RUN mkdir -p roms configs models system savestates saves screenshots

# Copy configuration files
COPY configs/ configs/
COPY models/ models/

# Set library paths (following working docker_emulator pattern)
ENV LD_LIBRARY_PATH="/work/stable-retro-scripts/ef_lib:/work/stable-retro-scripts/ef_lib/libtorch/lib:$LD_LIBRARY_PATH"
ENV PYTHONPATH="/work/stable-retro:/work/stable-retro-scripts:$PYTHONPATH"

# Expose ports for RetroArch network commands
EXPOSE 55355 8080

# Default command
CMD ["bash"]
