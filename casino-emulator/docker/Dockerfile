# syntax=docker/dockerfile:1
FROM --platform=linux/amd64 ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# 1. System dependencies for M1 Mac compatibility
RUN apt-get update && apt-get install -y \
    git cmake pkg-config zlib1g-dev libopenmpi-dev ffmpeg \
    libx11-dev libegl1-mesa-dev libgbm-dev libvulkan-dev \
    libpulse-dev libopenal-dev wget bzip2 libopencv-dev \
    build-essential python3 python3-pip python3-venv \
 && rm -rf /var/lib/apt/lists/*

# 2. Create Python virtual environment and install ML libs
RUN python3 -m venv /opt/casino-env \
 && /opt/casino-env/bin/pip install --upgrade pip \
 && /opt/casino-env/bin/pip install \
      "stable-baselines3[extra]" gymnasium torch torchvision opencv-python timm

# 3. Set environment to use virtual environment
ENV PATH="/opt/casino-env/bin:$PATH"
ENV VIRTUAL_ENV="/opt/casino-env"

# 4. Clone and install stable‑retro & scripts, including retro_ai_lib
RUN git clone https://github.com/Farama-Foundation/stable-retro.git /src/stable-retro \
 && git clone https://github.com/MatPoliquin/stable-retro-scripts.git /src/stable-retro-scripts \
 && cd /src/stable-retro \
 && pip install -e . \
 && cd /src/stable-retro-scripts \
 && pip install -e . \
 # Download PyTorch C++ library for retro_ai_lib
 && cd retro_ai_lib \
 && wget https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-2.3.1%2Bcpu.zip \
 && unzip libtorch-cxx11-abi-shared-with-deps-2.3.1+cpu.zip \
 # Build retro_ai_lib C++ plugin
 && mkdir -p build \
 && cd build \
 && cmake -DCMAKE_PREFIX_PATH=../libtorch -DUSE_PYTORCH=ON .. \
 && make -j$(nproc)

# 5. Clone & build RetroArchAI with retro_ai_lib linked
RUN git clone https://github.com/MatPoliquin/RetroArchAI.git /src/RetroArchAI \
 && mkdir -p /src/RetroArchAI/build \
 && cd /src/RetroArchAI/build \
 # Provide path to retro_ai_lib for CMake integration
 && cmake -DRETRO_AI_LIB_DIR=/src/stable-retro-scripts/retro_ai_lib/build .. \
 && make -j$(nproc)

# 6. Create necessary directories and set permissions
RUN mkdir -p /work/roms /work/configs /work/models \
 && chmod -R 755 /work

# 7. Set working directory & default to interactive shell
WORKDIR /work
CMD ["bash"]
