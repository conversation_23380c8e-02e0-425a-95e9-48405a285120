casino-emulator/
├── docker/               
│   └── Dockerfile        # Your build definition
├── src/                  
│   ├── stable-retro/     # git clone -e
│   ├── stable-retro-scripts/  # git clone -e
│   └── RetroArchAI/      # git clone from fork
├── roms/                 # Place ROM files here
├── models/               # Trained RL models output
│   ├── agent1/
│   ├── agent2/
│   └── ...
├── front/                # Next.js frontend
│   ├── public/
│   ├── pages/
│   └── components/
├── configs/              # Configuration files
│   ├── ai-controller.cfg
│   └── retroarch.cfg
├── scripts/              # Helper scripts (train.sh, serve.sh, etc.)
├── README.md
└── .gitignore
