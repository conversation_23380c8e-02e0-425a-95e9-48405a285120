'use client'
import React, { useEffect, useState } from 'react';
import {
  useAppKit,
  useAppKitAccount,
} from '@reown/appkit/react';
import Image from 'next/image';
import Link from 'next/link';
import { FaRobot, FaChartLine, FaTrophy, FaFire, FaEye, FaPlus, FaShoppingCart, FaDollarSign, FaTag } from 'react-icons/fa';

// Type definitions
type Agent = {
  id: string;
  name: string;
  tokenId: string;
  game: string;
  winRate: number;
  totalFights: number;
  wins: number;
  losses: number;
  trainingLoss: number;
  trainingSteps: number;
  status: string;
  strategy: string;
  lastTrained: string;
  image: string;
  rarity: string;
  trainingProgress: number;
  price: number;
  forSale: boolean;
  avgReward: number;
  episodeCount: number;
  owner?: string;
  isOwned?: boolean;
};

// Mock data for user's agents
const mockUserAgents: Agent[] = [
  {
    id: 'liu-kang-420',
    name: '<PERSON>',
    tokenId: '#420',
    game: 'Mortal Kombat II',
    winRate: 76,
    totalFights: 156,
    wins: 118,
    losses: 38,
    trainingLoss: 0.023,
    trainingSteps: 2500000,
    status: 'active',
    strategy: 'Aggressive',
    lastTrained: '2 hours ago',
    image: '/image/liukang.png',
    rarity: 'legendary',
    trainingProgress: 85,
    price: 12500,
    forSale: false,
    avgReward: 0.87,
    episodeCount: 15600,
    isOwned: true
  },
  {
    id: 'scorpion-069',
    name: 'Scorpion Jeet Eater',
    tokenId: '#069',
    game: 'Mortal Kombat II',
    winRate: 82,
    totalFights: 203,
    wins: 166,
    losses: 37,
    trainingLoss: 0.018,
    trainingSteps: 3200000,
    status: 'training',
    strategy: 'Balanced',
    lastTrained: '30 minutes ago',
    image: '/image/scorpionA3c.png',
    rarity: 'epic',
    trainingProgress: 45,
    price: 0,
    forSale: false,
    avgReward: 0.91,
    episodeCount: 20300,
    isOwned: true
  },
  {
    id: 'sub-zero-117',
    name: 'Sub-Zero Smolting',
    tokenId: '#117',
    game: 'Mortal Kombat II',
    winRate: 68,
    totalFights: 89,
    wins: 60,
    losses: 29,
    trainingLoss: 0.045,
    trainingSteps: 1800000,
    status: 'idle',
    strategy: 'Defensive',
    lastTrained: '1 day ago',
    image: '/image/subZero.png',
    rarity: 'rare',
    trainingProgress: 0,
    price: 7500,
    forSale: true,
    avgReward: 0.73,
    episodeCount: 8900,
    isOwned: true
  }
];

// Mock data for marketplace agents (other users)
const mockMarketplaceAgents: Agent[] = [
  {
    id: 'kitana-225',
    name: 'Kitana Waifu',
    tokenId: '#225',
    game: 'Mortal Kombat II',
    winRate: 79,
    totalFights: 134,
    wins: 106,
    losses: 28,
    trainingLoss: 0.031,
    trainingSteps: 2100000,
    status: 'available',
    strategy: 'Balanced',
    lastTrained: '4 hours ago',
    image: '/image/kitana.png',
    rarity: 'epic',
    trainingProgress: 0,
    price: 9500,
    forSale: true,
    avgReward: 0.82,
    episodeCount: 13400,
    owner: '0x1234...5678',
    isOwned: false
  },
  {
    id: 'baraka-333',
    name: 'Baraka Obama',
    tokenId: '#333',
    game: 'Mortal Kombat II',
    winRate: 85,
    totalFights: 198,
    wins: 168,
    losses: 30,
    trainingLoss: 0.015,
    trainingSteps: 4100000,
    status: 'available',
    strategy: 'Aggressive',
    lastTrained: '1 hour ago',
    image: '/image/baraka.png',
    rarity: 'legendary',
    trainingProgress: 0,
    price: 2100,
    forSale: true,
    avgReward: 0.94,
    episodeCount: 19800,
    owner: '0x9876...4321',
    isOwned: false
  },
  {
    id: 'sonic-addy',
    name: 'Sonic Addy',
    tokenId: '#456',
    game: 'Sonic the Hedgehog',
    winRate: 85,
    totalFights: 142,
    wins: 120,
    losses: 22,
    trainingLoss: 0.028,
    trainingSteps: 2800000,
    status: 'available',
    strategy: 'Speed',
    lastTrained: '6 hours ago',
    image: '/image/sonic.png',
    rarity: 'epic',
    trainingProgress: 0,
    price: 1100,
    forSale: true,
    avgReward: 0.88,
    episodeCount: 14200,
    owner: '0x5555...9999',
    isOwned: false
  }
];

const Marketplace: React.FC = () => {
  const [activeTab, setActiveTab] = useState('marketplace');
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('price-high');
  const [isVisible, setIsVisible] = useState(false);
  // const [searchTerm, setSearchTerm] = useState(''); // Reserved for future search functionality

  // Reown hooks
  const { open } = useAppKit();
  const { isConnected } = useAppKitAccount();
  // Note: walletInfo available but not needed for initial render

  // Add loading state for AppKit initialization
  const [isAppKitReady, setIsAppKitReady] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    // Set AppKit as ready immediately - wallet connection is not required for viewing
    setIsAppKitReady(true);
  }, []);

  // Helper functions
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8)]';
      case 'epic': return 'text-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.8)]';
      case 'rare': return 'text-[#00ff00] shadow-[0_0_5px_rgba(0,255,0,0.8)]';
      default: return 'text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'badge-success';
      case 'training': return 'badge-warning';
      case 'idle': return 'badge-ghost';
      case 'available': return 'badge-info';
      default: return 'badge-ghost';
    }
  };

  // Get current agents based on active tab
  const getCurrentAgents = () => {
    if (activeTab === 'collection') {
      return mockUserAgents;
    } else if (activeTab === 'marketplace') {
      return [...mockUserAgents.filter(a => a.forSale), ...mockMarketplaceAgents];
    }
    return mockMarketplaceAgents;
  };

  // Filter agents based on selected filter
  const filteredAgents = filter === 'all'
    ? getCurrentAgents()
    : getCurrentAgents().filter(agent => agent.game.toLowerCase().includes(filter.toLowerCase()));

  // Sort agents based on selected sort option
  const sortedAgents = [...filteredAgents].sort((a, b) => {
    switch (sortBy) {
      case 'price-high':
        return b.price - a.price;
      case 'price-low':
        return a.price - b.price;
      case 'win-rate':
        return b.winRate - a.winRate;
      case 'training-loss':
        return a.trainingLoss - b.trainingLoss; // Lower is better
      default:
        return 0;
    }
  });

  if (!isAppKitReady) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="font-['Orbitron'] text-3xl font-bold mb-4 text-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.8),0_0_10px_rgba(0,255,255,0.5),0_0_15px_rgba(0,255,255,0.3)]">
            LOADING MARKETPLACE...
          </h1>
          <div className="w-16 h-16 border-4 border-t-[#ff00ff] border-r-transparent border-b-[#00ffff] border-l-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`container mx-auto px-4 py-8 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
      {/* Header Section */}
      <div className="text-center mb-8">
        <h1 className="font-['Orbitron'] text-5xl font-bold mb-4 animate-[glitch_3s_infinite]">
          <span className="text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8),0_0_10px_rgba(255,0,255,0.5),0_0_15px_rgba(255,0,255,0.3)]">
            AI AGENT MARKETPLACE
          </span>
        </h1>
        <p className="max-w-2xl mx-auto mb-6">
          Manage your AI agent collection, view stats, and trade on the marketplace
        </p>

        {/* Wallet Connection */}
        {!isConnected && (
          <button
            onClick={() => open({ view: 'Connect' })}
            className="btn btn-primary mt-4 bg-gradient-to-r from-pink-600 to-purple-700 border-transparent shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)] hover:translate-y-[-2px] transition-all"
          >
            Connect Wallet
          </button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="flex justify-center mb-8">
        <div className="flex gap-2">
          <button
            className={`btn ${activeTab === 'collection' ? 'btn-primary bg-gradient-to-r from-[#ff00ff] to-[#00ffff] border-none' : 'btn-ghost'}`}
            onClick={() => setActiveTab('collection')}
          >
            <FaRobot className="mr-2" />
            My Collection
          </button>
          <button
            className={`btn ${activeTab === 'marketplace' ? 'btn-primary bg-gradient-to-r from-[#ff00ff] to-[#00ffff] border-none' : 'btn-ghost'}`}
            onClick={() => setActiveTab('marketplace')}
          >
            <FaShoppingCart className="mr-2" />
            Marketplace
          </button>
          <button
            className={`btn ${activeTab === 'stats' ? 'btn-primary bg-gradient-to-r from-[#ff00ff] to-[#00ffff] border-none' : 'btn-ghost'}`}
            onClick={() => setActiveTab('stats')}
          >
            <FaChartLine className="mr-2" />
            Stats
          </button>
        </div>
      </div>

      {/* Quick Stats - Show only on collection and stats tabs */}
      {(activeTab === 'collection' || activeTab === 'stats') && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="card bg-base-100 shadow-xl border border-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.5)]">
            <div className="card-body p-4 text-center">
              <FaRobot className="text-3xl text-[#00ffff] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#00ffff]">{mockUserAgents.length}</div>
              <div className="text-sm opacity-70">My Agents</div>
            </div>
          </div>
          <div className="card bg-base-100 shadow-xl border border-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.5)]">
            <div className="card-body p-4 text-center">
              <FaTrophy className="text-3xl text-[#ff00ff] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#ff00ff]">
                {Math.round(mockUserAgents.reduce((acc, agent) => acc + agent.winRate, 0) / mockUserAgents.length)}%
              </div>
              <div className="text-sm opacity-70">Avg Win Rate</div>
            </div>
          </div>
          <div className="card bg-base-100 shadow-xl border border-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.5)]">
            <div className="card-body p-4 text-center">
              <FaFire className="text-3xl text-[#00ffff] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#00ffff]">
                {mockUserAgents.reduce((acc, agent) => acc + agent.totalFights, 0)}
              </div>
              <div className="text-sm opacity-70">Total Fights</div>
            </div>
          </div>
          <div className="card bg-base-100 shadow-xl border border-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.5)]">
            <div className="card-body p-4 text-center">
              <FaChartLine className="text-3xl text-[#ff00ff] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#ff00ff]">
                {mockUserAgents.filter(agent => agent.status === 'training').length}
              </div>
              <div className="text-sm opacity-70">Training</div>
            </div>
          </div>
        </div>
      )}

      {/* Filter and Sort Controls */}
      <div className="mb-8 flex flex-wrap justify-between gap-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg font-['Orbitron'] text-sm border ${filter === 'all'
              ? 'bg-gradient-to-r from-pink-600 to-purple-700 border-transparent shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)]'
              : 'border-gray-600 hover:border-cyan-500'}`}
          >
            ALL AGENTS
          </button>
          <button
            onClick={() => setFilter('mortal kombat ii')}
            className={`px-4 py-2 rounded-lg font-['Orbitron'] text-sm border ${filter === 'mortal kombat ii'
              ? 'bg-gradient-to-r from-pink-600 to-purple-700 border-transparent shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)]'
              : 'border-gray-600 hover:border-cyan-500'}`}
          >
            MORTAL KOMBAT II
          </button>
          <button
            onClick={() => setFilter('sonic')}
            className={`px-4 py-2 rounded-lg font-['Orbitron'] text-sm border ${filter === 'sonic'
              ? 'bg-gradient-to-r from-pink-600 to-purple-700 border-transparent shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)]'
              : 'border-gray-600 hover:border-cyan-500'}`}
          >
            SONIC
          </button>
        </div>

        <div className="flex items-center">
          <span className="mr-2 text-sm">SORT BY:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-gray-900 border border-cyan-500 rounded px-2 py-1 text-sm font-['Share Tech Mono']"
          >
            <option value="price-high">Price (High to Low)</option>
            <option value="price-low">Price (Low to High)</option>
            <option value="win-rate">Win Rate</option>
            <option value="training-loss">Training Loss (Best First)</option>
          </select>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'stats' && (
        <div className="space-y-8">
          {/* Performance Overview */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title text-2xl text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8)]">
                Performance Overview
              </h2>
              <div className="h-64 bg-base-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Training Analytics Chart Coming Soon</p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title text-2xl text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8)]">
                Recent Activity
              </h2>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Agent</th>
                      <th>Activity</th>
                      <th>Result</th>
                      <th>Reward</th>
                      <th>Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Liu Kang #420</td>
                      <td>Fight vs Sub-Zero #117</td>
                      <td><span className="badge badge-success">Victory</span></td>
                      <td className="text-[#00ffff] font-bold">+250 DUMBS</td>
                      <td>2 hours ago</td>
                    </tr>
                    <tr>
                      <td>Scorpion #069</td>
                      <td>Training Session</td>
                      <td><span className="badge badge-warning">In Progress</span></td>
                      <td>-</td>
                      <td>30 min ago</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Agents Grid - Show for collection and marketplace tabs */}
      {(activeTab === 'collection' || activeTab === 'marketplace') && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {sortedAgents.map((agent) => (
            <div key={agent.id} className="card bg-base-100 shadow-xl border border-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.5)] hover:scale-105 transition-transform">
              <div className="card-body p-4">
                {/* Header with badges */}
                <div className="flex justify-between items-start mb-3">
                  <div className={`badge ${getRarityColor(agent.rarity)}`}>
                    {agent.rarity}
                  </div>
                  <div className="flex gap-1">
                    <div className={`badge ${getStatusColor(agent.status)}`}>
                      {agent.status}
                    </div>
                    {agent.forSale && (
                      <div className="badge badge-success">
                        <FaTag className="mr-1" />
                        For Sale
                      </div>
                    )}
                  </div>
                </div>

                {/* Agent Image */}
                <div className="w-full h-32 bg-base-200 rounded-lg mb-4 relative overflow-hidden">
                  <div className="w-full h-full relative">
                    {/* Background Image */}
                    <Image
                      src={agent.image}
                      alt={agent.name}
                      className="w-full h-full object-cover opacity-90"
                      width={200}
                      height={128}
                      onError={(e) => {
                        // On error, hide the image and show fallback
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />

                    {/* Fallback Robot Icon (hidden by default) */}
                    <div className="fallback-icon absolute inset-0 hidden items-center justify-center bg-base-200">
                      <FaRobot className="text-4xl text-[#ff00ff]" />
                    </div>

                    {/* Overlay gradient and robot icon */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute bottom-2 right-2 bg-black/70 rounded-full p-1.5">
                      <FaRobot className="text-sm text-[#ff00ff]" />
                    </div>

                    {/* Rarity glow effect */}
                    <div className={`absolute inset-0 rounded-lg ${
                      agent.rarity === 'legendary' ? 'shadow-[inset_0_0_20px_rgba(255,0,255,0.3)]' :
                      agent.rarity === 'epic' ? 'shadow-[inset_0_0_20px_rgba(0,255,255,0.3)]' :
                      'shadow-[inset_0_0_20px_rgba(0,255,0,0.2)]'
                    }`}></div>
                  </div>
                </div>

                {/* Agent Info */}
                <h3 className="font-bold text-lg text-[#00ffff] mb-1">
                  {agent.name} {agent.tokenId}
                </h3>
                <p className="text-sm opacity-70 mb-1">{agent.game}</p>
                {agent.owner && (
                  <p className="text-xs opacity-50 mb-3">
                    Owner: {agent.owner}
                  </p>
                )}

                {/* Stats Grid */}
                <div className="grid grid-cols-3 gap-2 text-center mb-4">
                  <div>
                    <div className="text-lg font-bold text-[#00ffff]">{agent.trainingLoss.toFixed(3)}</div>
                    <div className="text-xs opacity-70">Loss</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-[#ff00ff]">{agent.winRate}%</div>
                    <div className="text-xs opacity-70">Win Rate</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-[#00ffff]">{agent.totalFights}</div>
                    <div className="text-xs opacity-70">Fights</div>
                  </div>
                </div>

                {/* Training Progress */}
                {agent.status === 'training' && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Training Progress</span>
                      <span>{agent.trainingProgress}%</span>
                    </div>
                    <progress
                      className="progress progress-primary w-full"
                      value={agent.trainingProgress}
                      max="100"
                    ></progress>
                  </div>
                )}

                {/* Price */}
                {agent.forSale && (
                  <div className="text-center mb-4">
                    <div className="text-lg font-bold text-[#00ffff]">
                      <FaDollarSign className="inline mr-1" />
                      {agent.price} DUMBS
                    </div>
                    <div className="text-xs opacity-70">Sale Price</div>
                  </div>
                )}

                {/* Actions */}
                <div className="card-actions justify-center">
                  {agent.isOwned ? (
                    <div className="flex gap-1">
                      <button className="btn btn-sm btn-ghost text-[#00ffff]">
                        <FaEye className="mr-1" />
                        View
                      </button>
                      {!agent.forSale ? (
                        <button className="btn btn-sm btn-warning">
                          <FaTag className="mr-1" />
                          Sell
                        </button>
                      ) : (
                        <button className="btn btn-sm btn-ghost">
                          <FaTag className="mr-1" />
                          Listed
                        </button>
                      )}
                    </div>
                  ) : (
                    <button className="btn btn-sm btn-primary bg-gradient-to-r from-[#ff00ff] to-[#00ffff] border-none">
                      <FaShoppingCart className="mr-1" />
                      Buy Agent
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}

          {/* Add New Agent Card - Show only in collection tab */}
          {activeTab === 'collection' && (
            <div className="card bg-base-100 shadow-xl border-2 border-dashed border-[#ff00ff] hover:border-solid transition-all">
              <div className="card-body items-center justify-center text-center p-8">
                <FaPlus className="text-4xl text-[#ff00ff] mb-4" />
                <h3 className="font-bold text-lg text-[#ff00ff] mb-2">Train New Agent</h3>
                <p className="text-sm opacity-70 mb-4">Create a new AI agent via training</p>
                <Link href="/caballoloko" className="btn btn-primary bg-gradient-to-r from-[#ff00ff] to-[#00ffff] border-none">
                  Start Training
                </Link>
              </div>
            </div>
          )}
        </div>
      )}

      {/* How It Works Section */}
      <div className="mb-12 p-6 bg-gray-900 bg-opacity-30 rounded-lg border border-cyan-500 shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)]">
        <h2 className="font-['Orbitron'] text-2xl font-bold mb-6 text-center text-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.8),0_0_10px_rgba(0,255,255,0.5),0_0_15px_rgba(0,255,255,0.3)]">
          MARKETPLACE GUIDE
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-600 to-blue-600 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="font-['Orbitron'] text-xl font-bold">1</span>
            </div>
            <h3 className="font-['Orbitron'] text-lg font-bold mb-2 text-center text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8),0_0_10px_rgba(255,0,255,0.5),0_0_15px_rgba(255,0,255,0.3)]">
              BUY AGENTS
            </h3>
            <p className="text-sm text-center">
              Purchase pre-trained AI agent NFT&apos;S with unique skills to compete in your favorite games.
            </p>
          </div>
          <div className="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-600 to-blue-600 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="font-['Orbitron'] text-xl font-bold">2</span>
            </div>
            <h3 className="font-['Orbitron'] text-lg font-bold mb-2 text-center text-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.8),0_0_10px_rgba(0,255,255,0.5),0_0_15px_rgba(0,255,255,0.3)]">
              TRAIN, RE-ROLL, LEVEL UP
            </h3>
            <p className="text-sm text-center">
              Improve your agents skills through training sessions and gameplay. Re-Roll them for more training sessions. The fastest learning agents are more valuable.
            </p>
          </div>
          <div className="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-600 to-blue-600 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="font-['Orbitron'] text-xl font-bold">3</span>
            </div>
            <h3 className="font-['Orbitron'] text-lg font-bold mb-2 text-center text-[#ff00ff] shadow-[0_0_5px_rgba(255,0,255,0.8),0_0_10px_rgba(255,0,255,0.5),0_0_15px_rgba(255,0,255,0.3)]">
              SELL FOR PROFIT
            </h3>
            <p className="text-sm text-center">
              List your trained agents on the marketplace or escrow them allow  other players purchase them privately for whatever token you want.
            </p>
          </div>
        </div>
      </div>

      {/* Create Agent CTA */}
      <div className="text-center mb-12 p-6 bg-gray-900 bg-opacity-30 rounded-lg border border-cyan-500 shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)]">
        <h2 className="font-['Orbitron'] text-2xl font-bold mb-4 text-[#00ffff] shadow-[0_0_5px_rgba(0,255,255,0.8),0_0_10px_rgba(0,255,255,0.5),0_0_15px_rgba(0,255,255,0.3)]">
          CREATE YOUR OWN AGENT
        </h2>
        <p className="max-w-2xl mx-auto mb-6">
          Train your own AI agent from scratch and customize its learning parameters. Once trained, you can use it to compete or sell it on the marketplace.
        </p>
        <Link href="/caballoloko/" className="inline-block px-8 py-3 bg-gradient-to-r from-pink-600 to-purple-700 rounded-lg font-['Orbitron'] font-bold border border-transparent shadow-[0_0_10px_rgba(0,255,255,0.5),inset_0_0_10px_rgba(255,0,255,0.5)] text-lg hover:translate-y-[-2px] transition-all">
          CREATE AGENT
        </Link>
      </div>
    </div>
  );
};

export default Marketplace; 